root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-09 00:03:51
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\multitimeframe_scanner\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-09 00:03:51
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - 
=== Fyers API Authentication ===
fyers_config - INFO - A browser window will open for you to log in to Fyers.
fyers_config - INFO - After logging in, you will be redirected to Google.
fyers_config - INFO - Copy the auth code from the URL and paste it here.
fyers_config - INFO - 
Please login in the private browser window that opened.
fyers_config - INFO - Authentication files saved to C:\Users\<USER>\Desktop\Python\multitimeframe_scanner
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8265 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 EQUITY symbols for scanning
market_type_scanner - WARNING - No EQUITY symbols found for scanning
unified_scanner - WARNING - No EQUITY symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING INDEX SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized INDEX scanner
market_type_scanner - INFO - Starting INDEX scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['INDEX']
universal_symbol_parser - INFO - Performance stats: Processed 8265 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 INDEX symbols for scanning
market_type_scanner - WARNING - No INDEX symbols found for scanning
unified_scanner - WARNING - No INDEX symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING FUTURES SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized FUTURES scanner
market_type_scanner - INFO - Starting FUTURES scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
universal_symbol_parser - INFO - Loaded 6 symbols from NSE_FO.csv for market types: ['FUTURES']
universal_symbol_parser - INFO - Performance stats: Processed 76378 rows, found 6 valid symbols
universal_symbol_parser - INFO - Found 6 FUTURES symbols for scanning
market_type_scanner - INFO - Fetching market data for 6 FUTURES symbols
fyers_client - INFO - Batch 1/1 (6 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 6/6 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 6/6 symbols passed
market_type_scanner - INFO - LTP filter: 6/6 symbols passed
market_type_scanner - INFO - FUTURES scanning completed: 6 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\futures_scan_20250709_000504.csv
report_generator - INFO - Report contains 6 symbols
report_generator - INFO - Summary report created successfully: reports\futures_scan_summary_20250709_000504.txt
report_generator - INFO - Generated FUTURES reports:
report_generator - INFO -   CSV: reports\futures_scan_20250709_000504.csv
report_generator - INFO -   Summary: reports\futures_scan_summary_20250709_000504.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES symbols found: 6
unified_scanner - INFO - FUTURES CSV Report: reports\futures_scan_20250709_000504.csv
unified_scanner - INFO - FUTURES Summary Report: reports\futures_scan_summary_20250709_000504.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 50
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
universal_symbol_parser - INFO - Loaded 4458 symbols from NSE_FO.csv for market types: ['OPTIONS']
universal_symbol_parser - INFO - Performance stats: Processed 76378 rows, found 4458 valid symbols
universal_symbol_parser - INFO - Found 4458 OPTIONS symbols for scanning
market_type_scanner - INFO - Fetching market data for 4458 OPTIONS symbols
fyers_client - INFO - Batch 1/90 (50 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Batch 10/90 (50 symbols) - Rate: 4.3 batches/sec, ETA: 19s
fyers_client - WARNING - Rate limit hit for batch 18, attempt 1/5. Retrying after 3.0 seconds...
fyers_client - INFO - Batch 26/90 (50 symbols) - Rate: 1.0 batches/sec, ETA: 67s
fyers_client - INFO - Batch 50/90 (50 symbols) - Rate: 1.4 batches/sec, ETA: 28s
fyers_client - INFO - Batch 51/90 (50 symbols) - Rate: 1.4 batches/sec, ETA: 27s
fyers_client - INFO - Batch 76/90 (50 symbols) - Rate: 1.8 batches/sec, ETA: 8s
fyers_client - INFO - Batch 90/90 (8 symbols) - Rate: 2.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 4458/4458 symbols (100.0% batch success rate) in 44.8s
market_type_scanner - INFO - Volume filter: 4458/4458 symbols passed
market_type_scanner - INFO - LTP filter: 4458/4458 symbols passed
options_chain_filter - ERROR - Error creating Fyers client: stat: path should be string, bytes, os.PathLike or integer, not ConfigLoader
options_chain_filter - WARNING - No Fyers client available for fetching spot prices
options_chain_filter - INFO - Fetched spot prices for 0 underlyings
options_chain_filter - INFO - Created 6 options chains for BANKNIFTY
options_chain_filter - INFO - Created 14 options chains for NIFTY
options_chain_filter - INFO - Enhanced options filter: 2104/4458 options selected
market_type_scanner - INFO - Options chain filter: 2104/4458 symbols passed
market_type_scanner - INFO - OPTIONS scanning completed: 2104 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 2104 unpaired symbols
report_generator - INFO - Sorted 2104 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 2104 unpaired symbols
report_generator - INFO - CSV report created successfully: reports\options_scan_20250709_000550.csv
report_generator - INFO - Report contains 2104 symbols
report_generator - WARNING - Found 2104 unpaired symbols
report_generator - INFO - Sorted 2104 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 2104 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250709_000550.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250709_000550.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250709_000550.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 2104
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250709_000550.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250709_000550.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250709_000550.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-09 00:05:50
unified_scanner - INFO - Total symbols found: 2110
unified_scanner - INFO -   - EQUITY symbols: 0
unified_scanner - INFO -   - INDEX symbols: 0
unified_scanner - INFO -   - FUTURES symbols: 6
unified_scanner - INFO -   - OPTIONS symbols: 2104
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250709_000550.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-09 00:08:16
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\multitimeframe_scanner\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-09 00:08:16
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8265 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 EQUITY symbols for scanning
market_type_scanner - WARNING - No EQUITY symbols found for scanning
unified_scanner - WARNING - No EQUITY symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING INDEX SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized INDEX scanner
market_type_scanner - INFO - Starting INDEX scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['INDEX']
universal_symbol_parser - INFO - Performance stats: Processed 8265 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 INDEX symbols for scanning
market_type_scanner - WARNING - No INDEX symbols found for scanning
unified_scanner - WARNING - No INDEX symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING FUTURES SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized FUTURES scanner
market_type_scanner - INFO - Starting FUTURES scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
universal_symbol_parser - INFO - Loaded 6 symbols from NSE_FO.csv for market types: ['FUTURES']
universal_symbol_parser - INFO - Performance stats: Processed 76378 rows, found 6 valid symbols
universal_symbol_parser - INFO - Found 6 FUTURES symbols for scanning
market_type_scanner - INFO - Fetching market data for 6 FUTURES symbols
fyers_client - INFO - Batch 1/1 (6 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 6/6 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 6/6 symbols passed
market_type_scanner - INFO - LTP filter: 6/6 symbols passed
market_type_scanner - INFO - FUTURES scanning completed: 6 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\futures_scan_20250709_000820.csv
report_generator - INFO - Report contains 6 symbols
report_generator - INFO - Summary report created successfully: reports\futures_scan_summary_20250709_000820.txt
report_generator - INFO - Generated FUTURES reports:
report_generator - INFO -   CSV: reports\futures_scan_20250709_000820.csv
report_generator - INFO -   Summary: reports\futures_scan_summary_20250709_000820.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES symbols found: 6
unified_scanner - INFO - FUTURES CSV Report: reports\futures_scan_20250709_000820.csv
unified_scanner - INFO - FUTURES Summary Report: reports\futures_scan_summary_20250709_000820.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 50
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
universal_symbol_parser - INFO - Loaded 4458 symbols from NSE_FO.csv for market types: ['OPTIONS']
universal_symbol_parser - INFO - Performance stats: Processed 76378 rows, found 4458 valid symbols
universal_symbol_parser - INFO - Found 4458 OPTIONS symbols for scanning
market_type_scanner - INFO - Fetching market data for 4458 OPTIONS symbols
fyers_client - INFO - Batch 1/90 (50 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Batch 10/90 (50 symbols) - Rate: 4.8 batches/sec, ETA: 17s
fyers_client - INFO - Batch 26/90 (50 symbols) - Rate: 3.1 batches/sec, ETA: 21s
fyers_client - INFO - Batch 50/90 (50 symbols) - Rate: 3.4 batches/sec, ETA: 12s
fyers_client - INFO - Batch 51/90 (50 symbols) - Rate: 3.4 batches/sec, ETA: 12s
fyers_client - INFO - Batch 76/90 (50 symbols) - Rate: 3.6 batches/sec, ETA: 4s
fyers_client - INFO - Batch 90/90 (8 symbols) - Rate: 3.7 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 4458/4458 symbols (100.0% batch success rate) in 24.5s
market_type_scanner - INFO - Volume filter: 4458/4458 symbols passed
market_type_scanner - INFO - LTP filter: 4458/4458 symbols passed
options_chain_filter - ERROR - Error creating Fyers client: stat: path should be string, bytes, os.PathLike or integer, not ConfigLoader
options_chain_filter - WARNING - No Fyers client available for fetching spot prices
options_chain_filter - INFO - Fetched spot prices for 0 underlyings
options_chain_filter - INFO - Created 6 options chains for BANKNIFTY
options_chain_filter - INFO - Created 14 options chains for NIFTY
options_chain_filter - INFO - Enhanced options filter: 2104/4458 options selected
market_type_scanner - INFO - Options chain filter: 2104/4458 symbols passed
market_type_scanner - INFO - OPTIONS scanning completed: 2104 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 2104 unpaired symbols
report_generator - INFO - Sorted 2104 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 2104 unpaired symbols
report_generator - INFO - CSV report created successfully: reports\options_scan_20250709_000846.csv
report_generator - INFO - Report contains 2104 symbols
report_generator - WARNING - Found 2104 unpaired symbols
report_generator - INFO - Sorted 2104 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 2104 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250709_000846.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250709_000846.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250709_000846.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 2104
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250709_000846.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250709_000846.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250709_000846.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-09 00:08:46
unified_scanner - INFO - Total symbols found: 2110
unified_scanner - INFO -   - EQUITY symbols: 0
unified_scanner - INFO -   - INDEX symbols: 0
unified_scanner - INFO -   - FUTURES symbols: 6
unified_scanner - INFO -   - OPTIONS symbols: 2104
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250709_000846.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-09 00:10:38
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\multitimeframe_scanner\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-09 00:10:38
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
universal_symbol_parser - INFO - Loaded 1 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8265 rows, found 1 valid symbols
universal_symbol_parser - INFO - Found 1 EQUITY symbols for scanning
market_type_scanner - INFO - Fetching market data for 1 EQUITY symbols
fyers_client - INFO - Batch 1/1 (1 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.6s
market_type_scanner - INFO - Volume filter: 1/1 symbols passed
market_type_scanner - INFO - LTP filter: 1/1 symbols passed
market_type_scanner - INFO - EQUITY scanning completed: 1 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\equity_scan_20250709_001041.csv
report_generator - INFO - Report contains 1 symbols
report_generator - INFO - Summary report created successfully: reports\equity_scan_summary_20250709_001041.txt
report_generator - INFO - Generated EQUITY reports:
report_generator - INFO -   CSV: reports\equity_scan_20250709_001041.csv
report_generator - INFO -   Summary: reports\equity_scan_summary_20250709_001041.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY symbols found: 1
unified_scanner - INFO - EQUITY CSV Report: reports\equity_scan_20250709_001041.csv
unified_scanner - INFO - EQUITY Summary Report: reports\equity_scan_summary_20250709_001041.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING INDEX SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized INDEX scanner
market_type_scanner - INFO - Starting INDEX scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['INDEX']
universal_symbol_parser - INFO - Performance stats: Processed 8265 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 INDEX symbols for scanning
market_type_scanner - WARNING - No INDEX symbols found for scanning
unified_scanner - WARNING - No INDEX symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING FUTURES SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized FUTURES scanner
market_type_scanner - INFO - Starting FUTURES scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
universal_symbol_parser - INFO - Loaded 3 symbols from NSE_FO.csv for market types: ['FUTURES']
universal_symbol_parser - INFO - Performance stats: Processed 76378 rows, found 3 valid symbols
universal_symbol_parser - INFO - Found 3 FUTURES symbols for scanning
market_type_scanner - INFO - Fetching market data for 3 FUTURES symbols
fyers_client - INFO - Batch 1/1 (3 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 3/3 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 3/3 symbols passed
market_type_scanner - INFO - LTP filter: 3/3 symbols passed
market_type_scanner - INFO - FUTURES scanning completed: 3 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\futures_scan_20250709_001044.csv
report_generator - INFO - Report contains 3 symbols
report_generator - INFO - Summary report created successfully: reports\futures_scan_summary_20250709_001044.txt
report_generator - INFO - Generated FUTURES reports:
report_generator - INFO -   CSV: reports\futures_scan_20250709_001044.csv
report_generator - INFO -   Summary: reports\futures_scan_summary_20250709_001044.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES symbols found: 3
unified_scanner - INFO - FUTURES CSV Report: reports\futures_scan_20250709_001044.csv
unified_scanner - INFO - FUTURES Summary Report: reports\futures_scan_summary_20250709_001044.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 50
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
universal_symbol_parser - INFO - Loaded 532 symbols from NSE_FO.csv for market types: ['OPTIONS']
universal_symbol_parser - INFO - Performance stats: Processed 76378 rows, found 532 valid symbols
universal_symbol_parser - INFO - Found 532 OPTIONS symbols for scanning
market_type_scanner - INFO - Fetching market data for 532 OPTIONS symbols
fyers_client - INFO - Batch 1/11 (50 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Batch 10/11 (50 symbols) - Rate: 4.7 batches/sec, ETA: 0s
fyers_client - INFO - Batch 11/11 (32 symbols) - Rate: 4.7 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 532/532 symbols (100.0% batch success rate) in 2.6s
market_type_scanner - INFO - Volume filter: 532/532 symbols passed
market_type_scanner - INFO - LTP filter: 532/532 symbols passed
options_chain_filter - ERROR - Error creating Fyers client: stat: path should be string, bytes, os.PathLike or integer, not ConfigLoader
options_chain_filter - WARNING - No Fyers client available for fetching spot prices
options_chain_filter - INFO - Fetched spot prices for 0 underlyings
options_chain_filter - INFO - Created 3 options chains for RELIANCE
options_chain_filter - INFO - Enhanced options filter: 460/532 options selected
market_type_scanner - INFO - Options chain filter: 460/532 symbols passed
market_type_scanner - INFO - OPTIONS scanning completed: 460 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 460 unpaired symbols
report_generator - INFO - Sorted 460 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 460 unpaired symbols
report_generator - INFO - CSV report created successfully: reports\options_scan_20250709_001048.csv
report_generator - INFO - Report contains 460 symbols
report_generator - WARNING - Found 460 unpaired symbols
report_generator - INFO - Sorted 460 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 460 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250709_001048.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250709_001048.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250709_001048.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 460
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250709_001048.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250709_001048.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250709_001048.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-09 00:10:48
unified_scanner - INFO - Total symbols found: 464
unified_scanner - INFO -   - EQUITY symbols: 1
unified_scanner - INFO -   - INDEX symbols: 0
unified_scanner - INFO -   - FUTURES symbols: 3
unified_scanner - INFO -   - OPTIONS symbols: 460
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250709_001048.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-09 00:11:37
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\multitimeframe_scanner\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-09 00:11:37
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: True
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
universal_symbol_parser - INFO - Loaded 2041 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8265 rows, found 2041 valid symbols
universal_symbol_parser - INFO - Found 2041 EQUITY symbols for scanning
market_type_scanner - INFO - Fetching market data for 2041 EQUITY symbols
fyers_client - INFO - Batch 1/41 (50 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Batch 10/41 (50 symbols) - Rate: 4.1 batches/sec, ETA: 8s
fyers_client - INFO - Batch 26/41 (50 symbols) - Rate: 4.2 batches/sec, ETA: 4s
fyers_client - INFO - Batch 41/41 (41 symbols) - Rate: 3.7 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2041/2041 symbols (100.0% batch success rate) in 11.7s
market_type_scanner - INFO - Volume filter: 2041/2041 symbols passed
market_type_scanner - INFO - LTP filter: 2040/2041 symbols passed
market_type_scanner - INFO - EQUITY scanning completed: 2040 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\equity_scan_20250709_001151.csv
report_generator - INFO - Report contains 2040 symbols
report_generator - INFO - Summary report created successfully: reports\equity_scan_summary_20250709_001151.txt
report_generator - INFO - Generated EQUITY reports:
report_generator - INFO -   CSV: reports\equity_scan_20250709_001151.csv
report_generator - INFO -   Summary: reports\equity_scan_summary_20250709_001151.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY symbols found: 2040
unified_scanner - INFO - EQUITY CSV Report: reports\equity_scan_20250709_001151.csv
unified_scanner - INFO - EQUITY Summary Report: reports\equity_scan_summary_20250709_001151.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING INDEX SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: True
market_type_scanner - INFO - Initialized INDEX scanner
market_type_scanner - INFO - Starting INDEX scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
universal_symbol_parser - INFO - Loaded 116 symbols from NSE_CM.csv for market types: ['INDEX']
universal_symbol_parser - INFO - Performance stats: Processed 8265 rows, found 116 valid symbols
universal_symbol_parser - INFO - Found 116 INDEX symbols for scanning
market_type_scanner - INFO - Fetching market data for 116 INDEX symbols
fyers_client - INFO - Batch 1/3 (50 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Batch 2/3 (50 symbols) - Rate: 7.7 batches/sec, ETA: 0s
fyers_client - INFO - Batch 3/3 (16 symbols) - Rate: 4.5 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 116/116 symbols (100.0% batch success rate) in 1.0s
market_type_scanner - INFO - Volume filter skipped for INDEX market type
market_type_scanner - INFO - LTP filter: 116/116 symbols passed
market_type_scanner - INFO - INDEX scanning completed: 116 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\index_scan_20250709_001153.csv
report_generator - INFO - Report contains 116 symbols
report_generator - INFO - Summary report created successfully: reports\index_scan_summary_20250709_001153.txt
report_generator - INFO - Generated INDEX reports:
report_generator - INFO -   CSV: reports\index_scan_20250709_001153.csv
report_generator - INFO -   Summary: reports\index_scan_summary_20250709_001153.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - INDEX SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - INDEX symbols found: 116
unified_scanner - INFO - INDEX CSV Report: reports\index_scan_20250709_001153.csv
unified_scanner - INFO - INDEX Summary Report: reports\index_scan_summary_20250709_001153.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING FUTURES SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: True
market_type_scanner - INFO - Initialized FUTURES scanner
market_type_scanner - INFO - Starting FUTURES scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
universal_symbol_parser - INFO - Loaded 656 symbols from NSE_FO.csv for market types: ['FUTURES']
universal_symbol_parser - INFO - Performance stats: Processed 76378 rows, found 656 valid symbols
universal_symbol_parser - INFO - Found 656 FUTURES symbols for scanning
market_type_scanner - INFO - Fetching market data for 656 FUTURES symbols
fyers_client - INFO - Batch 1/14 (50 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Batch 10/14 (50 symbols) - Rate: 4.5 batches/sec, ETA: 1s
fyers_client - INFO - Batch 14/14 (6 symbols) - Rate: 4.5 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 656/656 symbols (100.0% batch success rate) in 3.4s
market_type_scanner - INFO - Volume filter: 656/656 symbols passed
market_type_scanner - INFO - LTP filter: 656/656 symbols passed
market_type_scanner - INFO - FUTURES scanning completed: 656 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\futures_scan_20250709_001158.csv
report_generator - INFO - Report contains 656 symbols
report_generator - INFO - Summary report created successfully: reports\futures_scan_summary_20250709_001158.txt
report_generator - INFO - Generated FUTURES reports:
report_generator - INFO -   CSV: reports\futures_scan_20250709_001158.csv
report_generator - INFO -   Summary: reports\futures_scan_summary_20250709_001158.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES symbols found: 656
unified_scanner - INFO - FUTURES CSV Report: reports\futures_scan_20250709_001158.csv
unified_scanner - INFO - FUTURES Summary Report: reports\futures_scan_summary_20250709_001158.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: True
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 50
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
universal_symbol_parser - INFO - Loaded 73560 symbols from NSE_FO.csv for market types: ['OPTIONS']
universal_symbol_parser - INFO - Performance stats: Processed 76378 rows, found 73560 valid symbols
universal_symbol_parser - INFO - Found 73560 OPTIONS symbols for scanning
market_type_scanner - INFO - Fetching market data for 73560 OPTIONS symbols
fyers_client - INFO - Batch 1/1472 (50 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Batch 10/1472 (50 symbols) - Rate: 4.9 batches/sec, ETA: 297s
fyers_client - INFO - Batch 26/1472 (50 symbols) - Rate: 4.4 batches/sec, ETA: 326s
fyers_client - INFO - Batch 50/1472 (50 symbols) - Rate: 4.3 batches/sec, ETA: 329s
fyers_client - INFO - Batch 51/1472 (50 symbols) - Rate: 4.3 batches/sec, ETA: 328s
fyers_client - INFO - Batch 76/1472 (50 symbols) - Rate: 4.3 batches/sec, ETA: 323s
fyers_client - INFO - Batch 100/1472 (50 symbols) - Rate: 3.6 batches/sec, ETA: 381s
fyers_client - INFO - Batch 101/1472 (50 symbols) - Rate: 3.6 batches/sec, ETA: 385s
fyers_client - INFO - Batch 126/1472 (50 symbols) - Rate: 3.5 batches/sec, ETA: 383s
fyers_client - WARNING - Rate limit hit for batch 150, attempt 1/5. Retrying after 3.0 seconds...
fyers_client - INFO - Batch 151/1472 (50 symbols) - Rate: 3.3 batches/sec, ETA: 397s
fyers_client - INFO - Batch 176/1472 (50 symbols) - Rate: 3.4 batches/sec, ETA: 380s
fyers_client - INFO - Batch 200/1472 (50 symbols) - Rate: 3.5 batches/sec, ETA: 365s
fyers_client - INFO - Batch 201/1472 (50 symbols) - Rate: 3.5 batches/sec, ETA: 364s
fyers_client - INFO - Batch 226/1472 (50 symbols) - Rate: 3.4 batches/sec, ETA: 362s
fyers_client - INFO - Batch 251/1472 (50 symbols) - Rate: 3.5 batches/sec, ETA: 347s
fyers_client - INFO - Batch 276/1472 (50 symbols) - Rate: 3.6 batches/sec, ETA: 336s
fyers_client - INFO - Batch 301/1472 (50 symbols) - Rate: 3.6 batches/sec, ETA: 325s
fyers_client - INFO - Batch 326/1472 (50 symbols) - Rate: 3.7 batches/sec, ETA: 313s
fyers_client - INFO - Batch 351/1472 (50 symbols) - Rate: 3.6 batches/sec, ETA: 309s
fyers_client - WARNING - Rate limit hit for batch 351, attempt 1/5. Retrying after 3.0 seconds...
fyers_client - WARNING - Rate limit hit for batch 351, attempt 2/5. Retrying after 6.0 seconds...
fyers_client - INFO - Batch 376/1472 (50 symbols) - Rate: 3.4 batches/sec, ETA: 326s
fyers_client - INFO - Batch 401/1472 (50 symbols) - Rate: 3.4 batches/sec, ETA: 314s
fyers_client - INFO - Batch 426/1472 (50 symbols) - Rate: 3.4 batches/sec, ETA: 303s
fyers_client - INFO - Batch 451/1472 (50 symbols) - Rate: 3.4 batches/sec, ETA: 299s
fyers_client - INFO - Batch 476/1472 (50 symbols) - Rate: 3.5 batches/sec, ETA: 288s
fyers_client - INFO - Batch 501/1472 (50 symbols) - Rate: 3.5 batches/sec, ETA: 278s
fyers_client - INFO - Batch 526/1472 (50 symbols) - Rate: 3.5 batches/sec, ETA: 268s
fyers_client - INFO - Batch 551/1472 (50 symbols) - Rate: 3.6 batches/sec, ETA: 259s
fyers_client - WARNING - Rate limit hit for batch 552, attempt 1/5. Retrying after 3.0 seconds...
fyers_client - INFO - Batch 576/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 285s
fyers_client - INFO - Batch 601/1472 (50 symbols) - Rate: 3.2 batches/sec, ETA: 274s
fyers_client - INFO - Batch 626/1472 (50 symbols) - Rate: 3.2 batches/sec, ETA: 264s
fyers_client - INFO - Batch 651/1472 (50 symbols) - Rate: 3.2 batches/sec, ETA: 256s
fyers_client - INFO - Batch 676/1472 (50 symbols) - Rate: 3.2 batches/sec, ETA: 246s
fyers_client - INFO - Batch 701/1472 (50 symbols) - Rate: 3.3 batches/sec, ETA: 236s
fyers_client - INFO - Batch 726/1472 (50 symbols) - Rate: 3.3 batches/sec, ETA: 227s
fyers_client - INFO - Batch 751/1472 (50 symbols) - Rate: 3.3 batches/sec, ETA: 217s
fyers_client - WARNING - Rate limit hit for batch 753, attempt 1/5. Retrying after 3.0 seconds...
fyers_client - INFO - Batch 776/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 227s
fyers_client - INFO - Batch 801/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 217s
fyers_client - INFO - Batch 826/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 208s
fyers_client - INFO - Batch 851/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 199s
fyers_client - INFO - Batch 876/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 190s
fyers_client - INFO - Batch 901/1472 (50 symbols) - Rate: 3.2 batches/sec, ETA: 181s
fyers_client - INFO - Batch 926/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 174s
fyers_client - INFO - Batch 951/1472 (50 symbols) - Rate: 3.2 batches/sec, ETA: 165s
fyers_client - WARNING - Rate limit hit for batch 954, attempt 1/5. Retrying after 3.0 seconds...
fyers_client - INFO - Batch 976/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 160s
fyers_client - INFO - Batch 1001/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 151s
fyers_client - INFO - Batch 1026/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 142s
fyers_client - INFO - Batch 1051/1472 (50 symbols) - Rate: 3.2 batches/sec, ETA: 133s
fyers_client - INFO - Batch 1076/1472 (50 symbols) - Rate: 3.2 batches/sec, ETA: 125s
fyers_client - INFO - Batch 1101/1472 (50 symbols) - Rate: 3.2 batches/sec, ETA: 117s
fyers_client - INFO - Batch 1126/1472 (50 symbols) - Rate: 3.2 batches/sec, ETA: 108s
fyers_client - INFO - Batch 1151/1472 (50 symbols) - Rate: 3.2 batches/sec, ETA: 100s
fyers_client - WARNING - Rate limit hit for batch 1155, attempt 1/5. Retrying after 3.0 seconds...
fyers_client - INFO - Batch 1176/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 97s
fyers_client - INFO - Batch 1201/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 88s
fyers_client - INFO - Batch 1226/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 80s
fyers_client - INFO - Batch 1251/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 71s
fyers_client - INFO - Batch 1276/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 63s
fyers_client - INFO - Batch 1301/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 55s
fyers_client - INFO - Batch 1326/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 47s
fyers_client - INFO - Batch 1351/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 39s
fyers_client - WARNING - Rate limit hit for batch 1356, attempt 1/5. Retrying after 3.0 seconds...
fyers_client - INFO - Batch 1376/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 31s
fyers_client - INFO - Batch 1401/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 23s
fyers_client - INFO - Batch 1426/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 15s
fyers_client - INFO - Batch 1451/1472 (50 symbols) - Rate: 3.1 batches/sec, ETA: 7s
fyers_client - INFO - Batch 1472/1472 (10 symbols) - Rate: 3.1 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 73560/73560 symbols (100.0% batch success rate) in 468.3s
market_type_scanner - INFO - Volume filter: 73560/73560 symbols passed
market_type_scanner - INFO - LTP filter: 73560/73560 symbols passed
options_chain_filter - ERROR - Error creating Fyers client: stat: path should be string, bytes, os.PathLike or integer, not ConfigLoader
options_chain_filter - WARNING - No Fyers client available for fetching spot prices
options_chain_filter - INFO - Fetched spot prices for 0 underlyings
options_chain_filter - INFO - Created 3 options chains for HFCL
options_chain_filter - INFO - Created 3 options chains for IOC
options_chain_filter - INFO - Created 3 options chains for IDFCFIRSTB
options_chain_filter - INFO - Created 3 options chains for HINDALCO
options_chain_filter - INFO - Created 3 options chains for IEX
options_chain_filter - INFO - Created 2 options chains for IRB
options_chain_filter - INFO - Created 3 options chains for IGL
options_chain_filter - INFO - Created 1 options chains for HINDCOPPER
options_chain_filter - INFO - Created 3 options chains for HINDPETRO
options_chain_filter - INFO - Created 3 options chains for HINDUNILVR
options_chain_filter - INFO - Created 3 options chains for IRCTC
options_chain_filter - INFO - Created 3 options chains for IIFL
options_chain_filter - INFO - Created 3 options chains for INDHOTEL
options_chain_filter - INFO - Created 3 options chains for HINDZINC
options_chain_filter - INFO - Created 3 options chains for INDIANB
options_chain_filter - INFO - Created 3 options chains for HUDCO
options_chain_filter - INFO - Created 3 options chains for INDIGO
options_chain_filter - INFO - Created 3 options chains for ICICIBANK
options_chain_filter - INFO - Created 3 options chains for INDUSINDBK
options_chain_filter - INFO - Created 3 options chains for INDUSTOWER
options_chain_filter - INFO - Created 3 options chains for JIOFIN
options_chain_filter - INFO - Created 3 options chains for INFY
options_chain_filter - INFO - Created 3 options chains for ICICIGI
options_chain_filter - INFO - Created 3 options chains for ICICIPRULI
options_chain_filter - INFO - Created 3 options chains for JSWSTEEL
options_chain_filter - INFO - Created 3 options chains for INOXWIND
options_chain_filter - INFO - Created 3 options chains for IDEA
options_chain_filter - INFO - Created 3 options chains for JUBLFOOD
options_chain_filter - INFO - Created 3 options chains for KALYANKJIL
options_chain_filter - INFO - Created 3 options chains for IREDA
options_chain_filter - INFO - Created 3 options chains for IRFC
options_chain_filter - INFO - Created 3 options chains for ITC
options_chain_filter - INFO - Created 3 options chains for KFINTECH
options_chain_filter - INFO - Created 3 options chains for JINDALSTEL
options_chain_filter - INFO - Created 3 options chains for KOTAKBANK
options_chain_filter - INFO - Created 3 options chains for KPITTECH
options_chain_filter - INFO - Created 2 options chains for JSL
options_chain_filter - INFO - Created 3 options chains for JSWENERGY
options_chain_filter - INFO - Created 3 options chains for LAURUSLABS
options_chain_filter - INFO - Created 3 options chains for LICHSGFIN
options_chain_filter - INFO - Created 3 options chains for LICI
options_chain_filter - INFO - Created 3 options chains for LODHA
options_chain_filter - INFO - Created 3 options chains for KEI
options_chain_filter - INFO - Created 3 options chains for LT
options_chain_filter - INFO - Created 3 options chains for LTF
options_chain_filter - INFO - Created 3 options chains for LUPIN
options_chain_filter - INFO - Created 3 options chains for M&M
options_chain_filter - INFO - Created 3 options chains for MANAPPURAM
options_chain_filter - INFO - Created 3 options chains for MARICO
options_chain_filter - INFO - Created 3 options chains for LTIM
options_chain_filter - INFO - Created 3 options chains for KAYNES
options_chain_filter - INFO - Created 3 options chains for MAXHEALTH
options_chain_filter - INFO - Created 1 options chains for M&MFIN
options_chain_filter - INFO - Created 3 options chains for MCX
options_chain_filter - INFO - Created 3 options chains for MARUTI
options_chain_filter - INFO - Created 3 options chains for MFSL
options_chain_filter - INFO - Created 1 options chains for MGL
options_chain_filter - INFO - Created 3 options chains for MOTHERSON
options_chain_filter - INFO - Created 3 options chains for MPHASIS
options_chain_filter - INFO - Created 3 options chains for MUTHOOTFIN
options_chain_filter - INFO - Created 3 options chains for NATIONALUM
options_chain_filter - INFO - Created 3 options chains for NAUKRI
options_chain_filter - INFO - Created 3 options chains for NBCC
options_chain_filter - INFO - Created 3 options chains for NCC
options_chain_filter - INFO - Created 3 options chains for NESTLEIND
options_chain_filter - INFO - Created 3 options chains for MANKIND
options_chain_filter - INFO - Created 3 options chains for NHPC
options_chain_filter - INFO - Created 3 options chains for NMDC
options_chain_filter - INFO - Created 3 options chains for NTPC
options_chain_filter - INFO - Created 3 options chains for NYKAA
options_chain_filter - INFO - Created 3 options chains for OBEROIRLTY
options_chain_filter - INFO - Created 3 options chains for OIL
options_chain_filter - INFO - Created 3 options chains for MAZDOCK
options_chain_filter - INFO - Created 3 options chains for ONGC
options_chain_filter - INFO - Created 3 options chains for PAGEIND
options_chain_filter - INFO - Created 3 options chains for PATANJALI
options_chain_filter - INFO - Created 3 options chains for OFSS
options_chain_filter - INFO - Created 3 options chains for PAYTM
options_chain_filter - INFO - Created 1 options chains for PEL
options_chain_filter - INFO - Created 3 options chains for PERSISTENT
options_chain_filter - INFO - Created 3 options chains for PETRONET
options_chain_filter - INFO - Created 3 options chains for PFC
options_chain_filter - INFO - Created 3 options chains for PHOENIXLTD
options_chain_filter - INFO - Created 3 options chains for PIDILITIND
options_chain_filter - INFO - Created 3 options chains for PIIND
options_chain_filter - INFO - Created 3 options chains for PNB
options_chain_filter - INFO - Created 3 options chains for PGEL
options_chain_filter - INFO - Created 3 options chains for PNBHOUSING
options_chain_filter - INFO - Created 3 options chains for POLICYBZR
options_chain_filter - INFO - Created 3 options chains for POLYCAB
options_chain_filter - INFO - Created 2 options chains for POONAWALLA
options_chain_filter - INFO - Created 3 options chains for POWERGRID
options_chain_filter - INFO - Created 3 options chains for PRESTIGE
options_chain_filter - INFO - Created 3 options chains for RBLBANK
options_chain_filter - INFO - Created 3 options chains for RECLTD
options_chain_filter - INFO - Created 3 options chains for RELIANCE
options_chain_filter - INFO - Created 3 options chains for SAIL
options_chain_filter - INFO - Created 3 options chains for SBICARD
options_chain_filter - INFO - Created 3 options chains for SBILIFE
options_chain_filter - INFO - Created 3 options chains for SBIN
options_chain_filter - INFO - Created 3 options chains for SHREECEM
options_chain_filter - INFO - Created 3 options chains for SHRIRAMFIN
options_chain_filter - INFO - Created 3 options chains for SIEMENS
options_chain_filter - INFO - Created 2 options chains for SJVN
options_chain_filter - INFO - Created 3 options chains for SOLARINDS
options_chain_filter - INFO - Created 3 options chains for SONACOMS
options_chain_filter - INFO - Created 3 options chains for SRF
options_chain_filter - INFO - Created 3 options chains for SUNPHARMA
options_chain_filter - INFO - Created 3 options chains for SUPREMEIND
options_chain_filter - INFO - Created 3 options chains for SYNGENE
options_chain_filter - INFO - Created 3 options chains for TATACHEM
options_chain_filter - INFO - Created 1 options chains for TATACOMM
options_chain_filter - INFO - Created 3 options chains for TATACONSUM
options_chain_filter - INFO - Created 3 options chains for TATAELXSI
options_chain_filter - INFO - Created 3 options chains for TATAMOTORS
options_chain_filter - INFO - Created 3 options chains for TATAPOWER
options_chain_filter - INFO - Created 3 options chains for TATASTEEL
options_chain_filter - INFO - Created 3 options chains for TATATECH
options_chain_filter - INFO - Created 3 options chains for PPLPHARMA
options_chain_filter - INFO - Created 3 options chains for TCS
options_chain_filter - INFO - Created 3 options chains for TECHM
options_chain_filter - INFO - Created 3 options chains for TIINDIA
options_chain_filter - INFO - Created 3 options chains for TITAGARH
options_chain_filter - INFO - Created 3 options chains for TITAN
options_chain_filter - INFO - Created 3 options chains for RVNL
options_chain_filter - INFO - Created 3 options chains for TORNTPHARM
options_chain_filter - INFO - Created 3 options chains for TORNTPOWER
options_chain_filter - INFO - Created 3 options chains for TRENT
options_chain_filter - INFO - Created 3 options chains for TVSMOTOR
options_chain_filter - INFO - Created 3 options chains for ULTRACEMCO
options_chain_filter - INFO - Created 3 options chains for UNIONBANK
options_chain_filter - INFO - Created 3 options chains for UNITDSPR
options_chain_filter - INFO - Created 3 options chains for UPL
options_chain_filter - INFO - Created 3 options chains for VBL
options_chain_filter - INFO - Created 3 options chains for VEDL
options_chain_filter - INFO - Created 3 options chains for VOLTAS
options_chain_filter - INFO - Created 3 options chains for WIPRO
options_chain_filter - INFO - Created 3 options chains for YESBANK
options_chain_filter - INFO - Created 3 options chains for ZYDUSLIFE
options_chain_filter - INFO - Created 3 options chains for UNOMINDA
options_chain_filter - INFO - Created 6 options chains for BANKNIFTY
options_chain_filter - INFO - Created 3 options chains for FINNIFTY
options_chain_filter - INFO - Created 14 options chains for NIFTY
options_chain_filter - INFO - Created 3 options chains for MIDCPNIFTY
options_chain_filter - INFO - Created 1 options chains for AARTIIND
options_chain_filter - INFO - Created 3 options chains for ABCAPITAL
options_chain_filter - INFO - Created 3 options chains for NIFTYNXT50
options_chain_filter - INFO - Created 3 options chains for ABB
options_chain_filter - INFO - Created 3 options chains for ALKEM
options_chain_filter - INFO - Created 3 options chains for ANGELONE
options_chain_filter - INFO - Created 3 options chains for AMBUJACEM
options_chain_filter - INFO - Created 3 options chains for ASHOKLEY
options_chain_filter - INFO - Created 3 options chains for 360ONE
options_chain_filter - INFO - Created 3 options chains for AUROPHARMA
options_chain_filter - INFO - Created 3 options chains for AXISBANK
options_chain_filter - INFO - Created 3 options chains for ADANIPORTS
options_chain_filter - INFO - Created 3 options chains for ADANIGREEN
options_chain_filter - INFO - Created 3 options chains for BHARATFORG
options_chain_filter - INFO - Created 3 options chains for APLAPOLLO
options_chain_filter - INFO - Created 3 options chains for APOLLOHOSP
options_chain_filter - INFO - Created 3 options chains for BOSCHLTD
options_chain_filter - INFO - Created 3 options chains for ASIANPAINT
options_chain_filter - INFO - Created 3 options chains for BSE
options_chain_filter - INFO - Created 3 options chains for ASTRAL
options_chain_filter - INFO - Created 3 options chains for AUBANK
options_chain_filter - INFO - Created 3 options chains for BANKBARODA
options_chain_filter - INFO - Created 1 options chains for ACC
options_chain_filter - INFO - Created 3 options chains for BEL
options_chain_filter - INFO - Created 2 options chains for ABFRL
options_chain_filter - INFO - Created 3 options chains for BHARTIARTL
options_chain_filter - INFO - Created 3 options chains for ADANIENSOL
options_chain_filter - INFO - Created 3 options chains for BPCL
options_chain_filter - INFO - Created 3 options chains for CANBK
options_chain_filter - INFO - Created 2 options chains for CESC
options_chain_filter - INFO - Created 3 options chains for CHOLAFIN
options_chain_filter - INFO - Created 3 options chains for ADANIENT
options_chain_filter - INFO - Created 3 options chains for CIPLA
options_chain_filter - INFO - Created 3 options chains for CGPOWER
options_chain_filter - INFO - Created 3 options chains for CUMMINSIND
options_chain_filter - INFO - Created 1 options chains for CHAMBLFERT
options_chain_filter - INFO - Created 3 options chains for EICHERMOT
options_chain_filter - INFO - Created 3 options chains for COALINDIA
options_chain_filter - INFO - Created 3 options chains for ETERNAL
options_chain_filter - INFO - Created 3 options chains for FEDERALBNK
options_chain_filter - INFO - Created 3 options chains for COLPAL
options_chain_filter - INFO - Created 3 options chains for GAIL
options_chain_filter - INFO - Created 3 options chains for GODREJCP
options_chain_filter - INFO - Created 3 options chains for HDFCAMC
options_chain_filter - INFO - Created 3 options chains for HDFCBANK
options_chain_filter - INFO - Created 3 options chains for CYIENT
options_chain_filter - INFO - Created 3 options chains for DIXON
options_chain_filter - INFO - Created 2 options chains for ATGL
options_chain_filter - INFO - Created 3 options chains for GLENMARK
options_chain_filter - INFO - Created 3 options chains for GMRAIRPORT
options_chain_filter - INFO - Created 3 options chains for HAVELLS
options_chain_filter - INFO - Created 3 options chains for HEROMOTOCO
options_chain_filter - INFO - Created 3 options chains for BHEL
options_chain_filter - INFO - Created 3 options chains for BIOCON
options_chain_filter - INFO - Created 3 options chains for BAJAJFINSV
options_chain_filter - INFO - Created 1 options chains for BALKRISIND
options_chain_filter - INFO - Created 3 options chains for BANDHANBNK
options_chain_filter - INFO - Created 1 options chains for BSOFT
options_chain_filter - INFO - Created 3 options chains for BDL
options_chain_filter - INFO - Created 3 options chains for BRITANNIA
options_chain_filter - INFO - Created 3 options chains for BLUESTARCO
options_chain_filter - INFO - Created 3 options chains for COFORGE
options_chain_filter - INFO - Created 3 options chains for CONCOR
options_chain_filter - INFO - Created 3 options chains for CAMS
options_chain_filter - INFO - Created 3 options chains for DABUR
options_chain_filter - INFO - Created 3 options chains for CDSL
options_chain_filter - INFO - Created 3 options chains for BANKINDIA
options_chain_filter - INFO - Created 3 options chains for DALBHARAT
options_chain_filter - INFO - Created 3 options chains for HCLTECH
options_chain_filter - INFO - Created 3 options chains for DLF
options_chain_filter - INFO - Created 3 options chains for DELHIVERY
options_chain_filter - INFO - Created 3 options chains for DMART
options_chain_filter - INFO - Created 3 options chains for DRREDDY
options_chain_filter - INFO - Created 3 options chains for EXIDEIND
options_chain_filter - INFO - Created 3 options chains for DIVISLAB
options_chain_filter - INFO - Created 3 options chains for FORTIS
options_chain_filter - INFO - Created 3 options chains for GODREJPROP
options_chain_filter - INFO - Created 3 options chains for GRASIM
options_chain_filter - INFO - Created 3 options chains for HDFCLIFE
options_chain_filter - INFO - Created 2 options chains for GRANULES
options_chain_filter - INFO - Created 3 options chains for HAL
options_chain_filter - INFO - Created 3 options chains for AMBER
options_chain_filter - INFO - Created 3 options chains for CROMPTON
options_chain_filter - INFO - Created 3 options chains for BAJFINANCE
options_chain_filter - INFO - Enhanced options filter: 67312/73560 options selected
market_type_scanner - INFO - Options chain filter: 67312/73560 symbols passed
market_type_scanner - INFO - OPTIONS scanning completed: 67312 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 67312 unpaired symbols
report_generator - INFO - Sorted 67312 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 67312 unpaired symbols
report_generator - INFO - CSV report created successfully: reports\options_scan_20250709_001950.csv
report_generator - INFO - Report contains 67312 symbols
report_generator - WARNING - Found 67312 unpaired symbols
report_generator - INFO - Sorted 67312 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 67312 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250709_001950.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250709_001950.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250709_001950.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 67312
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250709_001950.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250709_001950.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250709_001952.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-09 00:19:52
unified_scanner - INFO - Total symbols found: 70124
unified_scanner - INFO -   - EQUITY symbols: 2040
unified_scanner - INFO -   - INDEX symbols: 116
unified_scanner - INFO -   - FUTURES symbols: 656
unified_scanner - INFO -   - OPTIONS symbols: 67312
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250709_001952.txt
