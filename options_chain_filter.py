"""
Options Chain Filter for Multi-Market Type Scanner.
Handles filtering of options symbols to create option chains around ATM strikes.
Enhanced with intelligent filtering based on spot prices from EQUITY/INDEX markets.
"""

import logging
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from collections import defaultdict
from universal_symbol_parser import UniversalSymbol
from config_loader import ConfigLoader
from fyers_client import FyersClient, MarketData

logger = logging.getLogger(__name__)

@dataclass
class OptionsChainData:
    """Data structure for options chain information."""
    underlying: str
    expiry_year: str
    expiry_month: str
    spot_price: Optional[float] = None
    atm_strike: Optional[float] = None
    strike_multiplier: Optional[float] = None
    ce_options: List[UniversalSymbol] = None
    pe_options: List[UniversalSymbol] = None
    
    def __post_init__(self):
        if self.ce_options is None:
            self.ce_options = []
        if self.pe_options is None:
            self.pe_options = []


class OptionsChainFilter:
    """Filter for creating options chains around ATM strikes with intelligent spot price-based filtering."""

    def __init__(self, config: ConfigLoader):
        """
        Initialize the options chain filter.

        Args:
            config: Configuration loader instance
        """
        self.config = config
        self.strike_level = config.options_strike_level
        self.fyers_client = None

        # Strike interval mapping for different underlyings
        self.strike_intervals = {
            'NIFTY': 50,
            'BANKNIFTY': 100,
            'FINNIFTY': 50,
            'MIDCPNIFTY': 25,
            'SENSEX': 100,
            'BANKEX': 100,
        }

        logger.info(f"Options chain filter initialized with strike level: {self.strike_level}")

    def get_fyers_client(self) -> Optional[FyersClient]:
        """Get or create Fyers client for fetching spot prices."""
        if self.fyers_client is None:
            try:
                self.fyers_client = FyersClient(self.config)
                if not self.fyers_client.authenticate():
                    logger.error("Failed to authenticate Fyers client")
                    return None
            except Exception as e:
                logger.error(f"Error creating Fyers client: {e}")
                return None
        return self.fyers_client
    
    def get_spot_prices_for_underlyings(self, underlyings: List[str]) -> Dict[str, float]:
        """
        Get spot prices for underlying symbols from EQUITY/INDEX markets.

        Args:
            underlyings: List of underlying symbols to get spot prices for

        Returns:
            Dictionary mapping underlying -> spot price
        """
        spot_prices = {}
        client = self.get_fyers_client()

        if not client:
            logger.warning("No Fyers client available for fetching spot prices")
            return spot_prices

        try:
            # Create symbol list for fetching spot prices
            symbols_to_fetch = []
            for underlying in underlyings:
                # Try INDEX first, then EQUITY
                index_symbol = f"NSE:{underlying}-INDEX"
                equity_symbol = f"NSE:{underlying}-EQ"
                symbols_to_fetch.extend([index_symbol, equity_symbol])

            if not symbols_to_fetch:
                return spot_prices

            logger.info(f"Fetching spot prices for {len(underlyings)} underlyings...")
            market_data = client.get_market_data(symbols_to_fetch)

            for underlying in underlyings:
                # Check INDEX first, then EQUITY
                index_symbol = f"NSE:{underlying}-INDEX"
                equity_symbol = f"NSE:{underlying}-EQ"

                if index_symbol in market_data:
                    spot_prices[underlying] = market_data[index_symbol].ltp
                    logger.debug(f"Got INDEX spot price for {underlying}: {market_data[index_symbol].ltp}")
                elif equity_symbol in market_data:
                    spot_prices[underlying] = market_data[equity_symbol].ltp
                    logger.debug(f"Got EQUITY spot price for {underlying}: {market_data[equity_symbol].ltp}")
                else:
                    logger.warning(f"No spot price found for {underlying}")

            logger.info(f"Retrieved spot prices for {len(spot_prices)}/{len(underlyings)} underlyings")

        except Exception as e:
            logger.error(f"Error fetching spot prices: {e}")

        return spot_prices

    def get_strike_interval(self, underlying: str) -> float:
        """
        Get the strike interval for a given underlying symbol.

        Args:
            underlying: Underlying symbol

        Returns:
            Strike interval (e.g., 50 for NIFTY, 100 for BANKNIFTY)
        """
        return self.strike_intervals.get(underlying, 50.0)  # Default to 50

    def calculate_dynamic_strike_range(self, underlying: str, spot_price: float) -> Tuple[float, float]:
        """
        Calculate dynamic strike range based on spot price and strike interval.

        Args:
            underlying: Underlying symbol
            spot_price: Current spot price

        Returns:
            Tuple of (min_strike, max_strike) rounded to strike intervals
        """
        strike_interval = self.get_strike_interval(underlying)
        strike_range = self.strike_level * strike_interval

        # Calculate raw range
        min_strike_raw = spot_price - strike_range
        max_strike_raw = spot_price + strike_range

        # Round to nearest strike intervals
        min_strike = round(min_strike_raw / strike_interval) * strike_interval
        max_strike = round(max_strike_raw / strike_interval) * strike_interval

        logger.debug(f"Dynamic strike range for {underlying} (spot: {spot_price}): "
                    f"{min_strike} - {max_strike} (interval: {strike_interval})")

        return min_strike, max_strike

    def detect_strike_multiplier(self, options: List[UniversalSymbol]) -> float:
        """
        Detect the strike price multiplier for the given options.
        
        Args:
            options: List of options symbols
            
        Returns:
            Detected multiplier (e.g., 50, 100, 250, 500)
        """
        if not options:
            return 50.0  # Default multiplier
        
        # Collect all strike prices
        strikes = [opt.strike_price for opt in options if opt.strike_price is not None]
        
        if len(strikes) < 2:
            return 50.0
        
        # Sort strikes to find differences
        strikes.sort()
        
        # Calculate differences between consecutive strikes
        differences = []
        for i in range(1, len(strikes)):
            diff = strikes[i] - strikes[i-1]
            if diff > 0:
                differences.append(diff)
        
        if not differences:
            return 50.0
        
        # Find the most common difference (mode)
        diff_counts = {}
        for diff in differences:
            diff_counts[diff] = diff_counts.get(diff, 0) + 1
        
        # Get the most frequent difference
        most_common_diff = max(diff_counts.keys(), key=lambda x: diff_counts[x])
        
        logger.debug(f"Detected strike multiplier: {most_common_diff}")
        return most_common_diff
    
    def estimate_atm_strike(self, options: List[UniversalSymbol], 
                           spot_price: Optional[float] = None) -> Optional[float]:
        """
        Estimate the ATM (At The Money) strike price.
        
        Args:
            options: List of options symbols
            spot_price: Current spot price (if available)
            
        Returns:
            Estimated ATM strike price
        """
        if not options:
            return None
        
        # If we have spot price, use it to find nearest strike
        if spot_price is not None:
            strikes = [opt.strike_price for opt in options if opt.strike_price is not None]
            if strikes:
                # Find the strike closest to spot price
                closest_strike = min(strikes, key=lambda x: abs(x - spot_price))
                logger.debug(f"ATM strike based on spot price {spot_price}: {closest_strike}")
                return closest_strike
        
        # Otherwise, use the middle strike as approximation
        strikes = sorted([opt.strike_price for opt in options if opt.strike_price is not None])
        if strikes:
            middle_index = len(strikes) // 2
            atm_strike = strikes[middle_index]
            logger.debug(f"ATM strike estimated from middle: {atm_strike}")
            return atm_strike
        
        return None
    
    def group_options_by_expiry(self, options: List[UniversalSymbol]) -> Dict[Tuple[str, str], List[UniversalSymbol]]:
        """
        Group options by expiry (year + month).
        
        Args:
            options: List of options symbols
            
        Returns:
            Dictionary mapping (year, month) to list of options
        """
        grouped = defaultdict(list)
        
        for option in options:
            if option.is_options() and option.expiry_year and option.expiry_month:
                key = (option.expiry_year, option.expiry_month)
                grouped[key].append(option)
        
        return dict(grouped)
    
    def filter_options_around_atm(self, options: List[UniversalSymbol], 
                                 atm_strike: float, 
                                 strike_multiplier: float) -> Tuple[List[UniversalSymbol], List[UniversalSymbol]]:
        """
        Filter options to get CE and PE options around ATM strike.
        
        Args:
            options: List of options symbols
            atm_strike: ATM strike price
            strike_multiplier: Strike price multiplier
            
        Returns:
            Tuple of (CE options, PE options) within strike level range
        """
        # Calculate strike range
        strike_range = self.strike_level * strike_multiplier
        min_strike = atm_strike - strike_range
        max_strike = atm_strike + strike_range
        
        ce_options = []
        pe_options = []
        
        for option in options:
            if (option.strike_price is not None and 
                min_strike <= option.strike_price <= max_strike):
                
                if option.option_type == 'CE':
                    ce_options.append(option)
                elif option.option_type == 'PE':
                    pe_options.append(option)
        
        # Sort by strike price
        ce_options.sort(key=lambda x: x.strike_price)
        pe_options.sort(key=lambda x: x.strike_price)
        
        logger.debug(f"Filtered options around ATM {atm_strike}: {len(ce_options)} CE, {len(pe_options)} PE")
        return ce_options, pe_options

    def filter_options_with_dynamic_range(self, options: List[UniversalSymbol],
                                        underlying: str, spot_price: float) -> Tuple[List[UniversalSymbol], List[UniversalSymbol]]:
        """
        Filter options using dynamic strike range based on spot price and strike intervals.

        Args:
            options: List of options symbols
            underlying: Underlying symbol
            spot_price: Current spot price

        Returns:
            Tuple of (CE options, PE options) within dynamic strike range
        """
        min_strike, max_strike = self.calculate_dynamic_strike_range(underlying, spot_price)

        ce_options = []
        pe_options = []

        for option in options:
            if (option.strike_price is not None and
                min_strike <= option.strike_price <= max_strike):

                if option.option_type == 'CE':
                    ce_options.append(option)
                elif option.option_type == 'PE':
                    pe_options.append(option)

        # Sort by strike price
        ce_options.sort(key=lambda x: x.strike_price)
        pe_options.sort(key=lambda x: x.strike_price)

        logger.debug(f"Dynamic filtered options for {underlying} (spot: {spot_price}): "
                    f"{len(ce_options)} CE, {len(pe_options)} PE in range [{min_strike}, {max_strike}]")
        return ce_options, pe_options

    def create_options_chain(self, underlying: str, options: List[UniversalSymbol],
                           spot_price: Optional[float] = None) -> List[OptionsChainData]:
        """
        Create options chains for the given underlying and options.
        
        Args:
            underlying: Underlying symbol
            options: List of options symbols for this underlying
            spot_price: Current spot price (if available)
            
        Returns:
            List of OptionsChainData objects
        """
        chains = []
        
        # Group options by expiry
        expiry_groups = self.group_options_by_expiry(options)
        
        for (year, month), expiry_options in expiry_groups.items():
            try:
                # Detect strike multiplier for this expiry
                strike_multiplier = self.detect_strike_multiplier(expiry_options)
                
                # Estimate ATM strike
                atm_strike = self.estimate_atm_strike(expiry_options, spot_price)
                
                if atm_strike is None:
                    logger.warning(f"Could not determine ATM strike for {underlying} {year}{month}")
                    continue
                
                # Filter options around ATM
                ce_options, pe_options = self.filter_options_around_atm(
                    expiry_options, atm_strike, strike_multiplier
                )
                
                # Only create chain if we have both CE and PE options
                if ce_options and pe_options:
                    chain = OptionsChainData(
                        underlying=underlying,
                        expiry_year=year,
                        expiry_month=month,
                        spot_price=spot_price,
                        atm_strike=atm_strike,
                        strike_multiplier=strike_multiplier,
                        ce_options=ce_options,
                        pe_options=pe_options
                    )
                    chains.append(chain)
                    
                    logger.debug(f"Created options chain for {underlying} {year}{month}: "
                               f"{len(ce_options)} CE, {len(pe_options)} PE options")
                else:
                    logger.debug(f"Skipping {underlying} {year}{month}: insufficient CE/PE pairs")
                    
            except Exception as e:
                logger.error(f"Error creating options chain for {underlying} {year}{month}: {e}")
                continue
        
        logger.info(f"Created {len(chains)} options chains for {underlying}")
        return chains
    
    def filter_options_symbols(self, options: List[UniversalSymbol],
                             spot_prices: Optional[Dict[str, float]] = None) -> List[UniversalSymbol]:
        """
        Filter options symbols to create option chains around ATM strikes with intelligent spot price-based filtering.

        Args:
            options: List of options symbols
            spot_prices: Optional dictionary of underlying -> spot price

        Returns:
            Filtered list of options symbols that form valid chains
        """
        if not options:
            return []

        # Group options by underlying
        underlying_groups = defaultdict(list)
        for option in options:
            if option.is_options():
                underlying_groups[option.underlying].append(option)

        # If no spot prices provided, fetch them from EQUITY/INDEX markets
        if not spot_prices:
            underlyings = list(underlying_groups.keys())
            spot_prices = self.get_spot_prices_for_underlyings(underlyings)
            logger.info(f"Fetched spot prices for {len(spot_prices)} underlyings")

        filtered_options = []

        for underlying, underlying_options in underlying_groups.items():
            try:
                spot_price = spot_prices.get(underlying) if spot_prices else None

                if spot_price:
                    # Use dynamic filtering based on spot price and strike intervals
                    logger.debug(f"Using dynamic filtering for {underlying} with spot price {spot_price}")

                    # Group by expiry and apply dynamic filtering
                    expiry_groups = self.group_options_by_expiry(underlying_options)

                    for (year, month), expiry_options in expiry_groups.items():
                        ce_options, pe_options = self.filter_options_with_dynamic_range(
                            expiry_options, underlying, spot_price
                        )

                        # Only include if we have both CE and PE options
                        if ce_options and pe_options:
                            filtered_options.extend(ce_options)
                            filtered_options.extend(pe_options)
                            logger.debug(f"Added {len(ce_options)} CE and {len(pe_options)} PE options "
                                       f"for {underlying} {year}{month}")
                else:
                    # Fallback to original method if no spot price available
                    logger.debug(f"No spot price for {underlying}, using original filtering")
                    chains = self.create_options_chain(underlying, underlying_options, None)

                    for chain in chains:
                        filtered_options.extend(chain.ce_options)
                        filtered_options.extend(chain.pe_options)

            except Exception as e:
                logger.error(f"Error filtering options for {underlying}: {e}")
                continue

        logger.info(f"Enhanced options filter: {len(filtered_options)}/{len(options)} options selected")
        return filtered_options
    
    def get_options_summary(self, chains: List[OptionsChainData]) -> Dict[str, any]:
        """
        Get summary statistics for options chains.
        
        Args:
            chains: List of options chains
            
        Returns:
            Summary statistics dictionary
        """
        if not chains:
            return {}
        
        total_ce = sum(len(chain.ce_options) for chain in chains)
        total_pe = sum(len(chain.pe_options) for chain in chains)
        unique_underlyings = len(set(chain.underlying for chain in chains))
        
        return {
            'total_chains': len(chains),
            'unique_underlyings': unique_underlyings,
            'total_ce_options': total_ce,
            'total_pe_options': total_pe,
            'total_options': total_ce + total_pe
        }
