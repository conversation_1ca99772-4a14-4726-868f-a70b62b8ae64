"""
Main entry point for the Index Scanner application.
Orchestrates the entire scanning process from configuration loading to report generation.
"""

import logging
import sys
import os
import subprocess
from datetime import datetime

# Import our modules
from fyers_config import setup_logging
from config_loader import get_config
from report_generator import ReportGenerator

def main():
    """Main function that orchestrates the unified scanning process."""
    
    # Setup logging
    logger = setup_logging(level=logging.INFO, log_to_file=True)
    
    try:
        logger.info("=" * 80)
        logger.info("UNIFIED SCANNER APPLICATION STARTED")
        logger.info("=" * 80)
        logger.info(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Import and use the unified scanner
        from unified_scanner import UnifiedScanner
        
        # Initialize unified scanner
        scanner = UnifiedScanner("config.yaml")
        
        # Run unified scan
        success = scanner.run_unified_scan()
        
        return success
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        print("\nApplication interrupted by user")
        return False

    except Exception as e:
        logger.error(f"Application failed with error: {e}", exc_info=True)
        print(f"\nApplication failed: {e}")
        print("Check the log files for detailed error information")
        return False

def print_banner():
    """Print application banner."""
    banner = """
    =========================================================================
                            UNIFIED SCANNER
                            ---------------
      > Scans EQUITY symbols from NSE_CM.csv (ending with -EQ)
      > Scans OPTIONS symbols from NSE_FO.csv (monthly options)
      > Processes NSE_CM.csv first, then NSE_FO.csv
      > Filters by Volume, LTP, Price Pairing CE PE Filter, MAE Indicator 
        based on config.yaml settings.
      > Generates separate CSV and Text reports for each flow.
    =========================================================================
    """
    print(banner)

def check_prerequisites():
    """Check if all required files and dependencies are available."""
    required_files = ["config.yaml"]
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"Missing required files: {', '.join(missing_files)}")
        return False

    # CSV files will be downloaded automatically, so we don't check for them here
    csv_files = ["NSE_CM.csv", "NSE_FO.csv"]
    missing_csv = []
    for file in csv_files:
        if not os.path.exists(file):
            missing_csv.append(file)
    
    if missing_csv:
        print(f"CSV files will be downloaded: {', '.join(missing_csv)}")

    try:
        # Check if required packages are available
        import yaml
        import fyers_apiv3
        print("All prerequisites satisfied")
        return True
    except ImportError as e:
        print(f"Missing required package: {e}")
        print("Please install required packages using: pip install pyyaml fyers-apiv3")
        return False

BASE_NIFTY50_SYMBOLS = ['ADANIENT', 'ADANIPORTS', 'APOLLOHOSP', 'ASIANPAINT', 'AXISBANK', 'BAJAJ', 
                        'BAJFINANCE', 'BAJAJFINSV', 'BEL', 'BHARTIARTL', 'BRITANNIA', 'CIPLA', 
                        'COALINDIA', 'DRREDDY', 'EICHERMOT', 'GRASIM', 'HCLTECH', 'HDFCBANK', 
                        'HDFCLIFE', 'HEROMOTOCO', 'HINDALCO', 'HINDUNILVR', 'ICICIBANK', 'INFY', 
                        'INDUSINDBK', 'ITC', 'JIOFIN', 'JSWSTEEL', 'KOTAKBANK', 'LT', 'M&M', 
                        'MARUTI', 'NTPC', 'NESTLEIND', 'ONGC', 'POWERGRID', 'RELIANCE', 'SBILIFE', 
                        'SBIN', 'SHRIRAMFIN', 'SUNPHARMA', 'TCS', 'TATACONSUM', 'TATAMOTORS', 
                        'TATASTEEL', 'TECHM', 'TITAN', 'TRENT', 'ULTRACEMCO', 'WIPRO']

if __name__ == "__main__":
    # Print banner
    print_banner()
    
    # Change working directory to the script's directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))

    # Check prerequisites
    if not check_prerequisites():
        sys.exit(1)
    
    # Run main application (unified scanner handles symbol downloading)
    success = main()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)