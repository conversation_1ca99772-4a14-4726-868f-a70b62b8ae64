"""
Symbol parser for extracting equity symbols from NSE_CM.csv.
Filters symbols ending with -EQ pattern for cash market equity trading.
"""

import csv
import logging
import re
from typing import List, Dict, Set, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class EquitySymbol:
    """Data class to represent an equity symbol."""
    symbol: str
    underlying: str
    full_symbol: str  # NSE:ABAN-EQ format

class EquitySymbolParser:
    """Parser for extracting and filtering equity symbols from NSE_CM.csv."""
    
    def __init__(self, csv_file_path: str = "NSE_CM.csv", target_symbols: Optional[List[str]] = None):
        """
        Initialize the equity symbol parser.

        Args:
            csv_file_path: Path to the NSE_CM.csv file
            target_symbols: List of underlying symbols to filter (e.g., ['RELIANCE', 'TCS'])
                          If None, processes all available symbols
                          If contains 'ALL', processes all available symbols and ignores other symbols
        """
        self.csv_file_path = csv_file_path
        self.use_all_symbols = False

        if target_symbols is None:
            self.use_all_symbols = True
            try:
                self.target_symbols = self._extract_all_underlying_symbols()
                logger.info(f"Processing all available equity symbols: {len(self.target_symbols)} unique underlyings found")
            except Exception as e:
                logger.warning(f"Could not extract symbols from CSV during initialization: {e}")
                self.target_symbols = set()
        elif 'ALL' in target_symbols:
            # When 'ALL' is present, ignore other symbols and process all available symbols
            self.use_all_symbols = True
            try:
                self.target_symbols = self._extract_all_underlying_symbols()
                logger.info(f"'ALL' detected - processing all available equity symbols: {len(self.target_symbols)} unique underlyings found")
                logger.info(f"Available equity underlyings: {', '.join(sorted(list(self.target_symbols)[:10]))}{'...' if len(self.target_symbols) > 10 else ''}")
            except Exception as e:
                logger.warning(f"Could not extract symbols from CSV during initialization: {e}")
                self.target_symbols = set()
        else:
            self.target_symbols = set(target_symbols)
            logger.info(f"Using configured target equity symbols: {', '.join(sorted(self.target_symbols))}")

        # Create dynamic regex pattern for configured symbols
        self._create_symbol_pattern()

    def _extract_all_underlying_symbols(self) -> Set[str]:
        """
        Extract all unique underlying symbols from the NSE_CM.csv file.
        This is used when 'ALL' is specified in the configuration.

        Returns:
            Set of all unique underlying symbols found in the CSV
        """
        underlying_symbols = set()


        try:
            with open(self.csv_file_path, 'r', encoding='utf-8') as file:
                csv_reader = csv.reader(file)

                for row_num, row in enumerate(csv_reader, 1):
                    try:
                        # Column J (index 9) contains the NSE symbol
                        if len(row) > 9:
                            nse_symbol = row[9].strip()

                            # Remove NSE: prefix if present
                            symbol = nse_symbol.replace('NSE:', '')

                            # Check if symbol ends with -EQ or -INDEX
                            if symbol.endswith('-EQ'):
                                # Extract underlying by removing -EQ suffix
                                underlying = symbol[:-3]  # Remove last 3 characters (-EQ)
                            elif symbol.endswith('-INDEX'):
                                # Extract underlying by removing -INDEX suffix
                                underlying = symbol[:-6]  # Remove last 6 characters (-INDEX)
                            else:
                                continue

                            # Validate that it's a proper underlying
                            # Must be at least 1 character and contain only valid characters
                            if len(underlying) >= 1 and re.match(r'^[A-Z0-9&]+$', underlying):
                                underlying_symbols.add(underlying)

                    except Exception as e:
                        logger.debug(f"Error processing row {row_num} for underlying extraction: {e}")
                        continue

        except FileNotFoundError:
            logger.warning(f"CSV file not found: {self.csv_file_path}")
            return set()
        except Exception as e:
            logger.warning(f"Error reading CSV file for underlying extraction: {e}")
            return set()

        logger.info(f"Extracted {len(underlying_symbols)} unique equity underlying symbols from CSV")

        return underlying_symbols

    def _create_symbol_pattern(self) -> None:
        """
        Create dynamic regex pattern based on configured target symbols.
        Pattern: UNDERLYING + -EQ

        For performance optimization when using 'ALL', we use a general pattern
        and validate underlying symbols separately.
        """
        if not self.target_symbols:
            # If no target symbols, use general pattern to match any equity or index symbol
            self.symbol_pattern = r'^([A-Z0-9&]+)-(EQ|INDEX)$'
            logger.info("Using general pattern for equity and index symbols")
        elif self.use_all_symbols and len(self.target_symbols) > 20:
            # Performance optimization: Use general pattern for many symbols
            # and validate underlying separately in parse method
            self.symbol_pattern = r'^([A-Z0-9&]+)-(EQ|INDEX)$'
            logger.info(f"Using optimized general pattern for {len(self.target_symbols)} equity and index symbols")
        else:
            # Use specific pattern for smaller symbol sets
            escaped_symbols = [re.escape(symbol) for symbol in sorted(self.target_symbols)]
            symbols_pattern = '|'.join(escaped_symbols)

            # Create the complete pattern: (SYMBOL1|SYMBOL2|...)-(EQ|INDEX)
            self.symbol_pattern = rf'^({symbols_pattern})-(EQ|INDEX)$'
            logger.debug(f"Created specific equity/index symbol pattern for {len(self.target_symbols)} symbols")

        logger.debug(f"Equity/Index symbol pattern: {self.symbol_pattern}")
        
    def parse_symbol_from_nse_format(self, nse_symbol: str) -> Optional[EquitySymbol]:
        """
        Parse NSE format symbol like 'NSE:ABAN-EQ' into components.
        Only accepts symbols matching the pattern: UNDERLYING + -EQ

        Args:
            nse_symbol: Symbol in NSE format

        Returns:
            EquitySymbol object or None if parsing fails or doesn't match the required pattern
        """
        try:
            # Remove NSE: prefix if present
            symbol = nse_symbol.replace('NSE:', '')

            # Use the dynamic pattern created for configured symbols
            # Pattern matches: UNDERLYING + -(EQ|INDEX)
            # Example: ABAN-EQ, RELIANCE-EQ, TCS-EQ, NIFTY50-INDEX
            match = re.match(self.symbol_pattern, symbol)

            if not match:
                # Symbol doesn't match the required pattern
                return None

            underlying = match.group(1)
            suffix = match.group(2)

            # Strict validation: Ensure underlying is valid if we have specific targets
            if self.target_symbols and underlying not in self.target_symbols:
                return None

            # Additional validation: Ensure underlying contains only valid characters
            if not re.match(r'^[A-Z0-9&]+$', underlying):
                return None

            # Additional validation: Ensure underlying is not empty
            if not underlying:
                return None

            return EquitySymbol(
                symbol=symbol,
                underlying=underlying,
                full_symbol=nse_symbol
            )

        except Exception as e:
            logger.debug(f"Failed to parse equity symbol {nse_symbol}: {e}")
            return None
    
    def load_symbols_from_csv(self, limit_symbols: Optional[int] = None) -> List[EquitySymbol]:
        """
        Load and parse equity symbols from NSE_CM.csv file with performance optimizations.

        Args:
            limit_symbols: Optional limit for testing with subset of symbols (for performance testing)

        Returns:
            List of EquitySymbol objects
        """
        symbols = []
        processed_count = 0
        valid_symbols_count = 0

        try:
            with open(self.csv_file_path, 'r', encoding='utf-8') as file:
                csv_reader = csv.reader(file)

                for row_num, row in enumerate(csv_reader, 1):
                    try:
                        # Performance: Early exit if limit reached
                        if limit_symbols and len(symbols) >= limit_symbols:
                            logger.info(f"Reached symbol limit of {limit_symbols}, stopping processing")
                            break

                        # Column J (index 9) contains the NSE symbol
                        if len(row) > 9:
                            nse_symbol = row[9].strip()
                            processed_count += 1


                        # Performance: Quick pre-filter before expensive parsing
                        # Skip if doesn't end with -EQ or -INDEX
                        if not (nse_symbol.endswith('-EQ') or nse_symbol.endswith('-INDEX')):
                            continue

                            # Parse the symbol
                            parsed_symbol = self.parse_symbol_from_nse_format(nse_symbol)

                            if parsed_symbol:
                                valid_symbols_count += 1
                                symbols.append(parsed_symbol)

                                # Performance: Log progress for large datasets
                                if len(symbols) % 1000 == 0:
                                    logger.info(f"Processed {len(symbols)} valid equity symbols so far...")

                    except Exception as e:
                        logger.debug(f"Error processing row {row_num}: {e}")
                        continue

        except FileNotFoundError:
            logger.error(f"CSV file not found: {self.csv_file_path}")
            raise
        except Exception as e:
            logger.error(f"Error reading CSV file: {e}")
            raise

        logger.info(f"Performance stats: Processed {processed_count} rows, found {valid_symbols_count} valid equity symbols, "
                   f"total symbols loaded: {len(symbols)}")
        return symbols
    
    def filter_symbols_by_underlying(self, symbols: List[EquitySymbol], 
                                   underlying_symbols: List[str]) -> List[EquitySymbol]:
        """
        Filter symbols by underlying symbols.
        
        Args:
            symbols: List of EquitySymbol objects
            underlying_symbols: List of underlying symbols to filter by
            
        Returns:
            Filtered list of EquitySymbol objects
        """
        filtered = [s for s in symbols if s.underlying in underlying_symbols]
        logger.info(f"Filtered to {len(filtered)} equity symbols for underlyings: {underlying_symbols}")
        return filtered
    
    def get_symbols_for_scanning(self, underlying_symbols: List[str] = None, limit_symbols: Optional[int] = None) -> List[str]:
        """
        Get list of equity symbols ready for market data scanning.

        Args:
            underlying_symbols: List of underlying symbols to filter by
            limit_symbols: Optional limit for testing with subset of symbols

        Returns:
            List of symbol strings in NSE format
        """
        if underlying_symbols is None:
            underlying_symbols = list(self.target_symbols)

        # Load symbols from CSV with optional limit
        all_symbols = self.load_symbols_from_csv(limit_symbols=limit_symbols)

        # Filter by underlying symbols if specified
        if underlying_symbols:
            filtered_symbols = self.filter_symbols_by_underlying(all_symbols, underlying_symbols)
        else:
            filtered_symbols = all_symbols

        # Extract full symbol strings
        symbol_strings = [s.full_symbol for s in filtered_symbols]

        logger.info(f"Prepared {len(symbol_strings)} equity symbols for scanning")
        return symbol_strings
    
    def get_symbol_details(self, underlying_symbols: List[str] = None, limit_symbols: Optional[int] = None) -> List[EquitySymbol]:
        """
        Get detailed equity symbol information for analysis.

        Args:
            underlying_symbols: List of underlying symbols to filter by
            limit_symbols: Optional limit for testing with subset of symbols

        Returns:
            List of EquitySymbol objects with detailed information
        """
        if underlying_symbols is None:
            underlying_symbols = list(self.target_symbols)

        # Load symbols from CSV with optional limit
        all_symbols = self.load_symbols_from_csv(limit_symbols=limit_symbols)

        # Filter by underlying symbols if specified
        if underlying_symbols:
            filtered_symbols = self.filter_symbols_by_underlying(all_symbols, underlying_symbols)
        else:
            filtered_symbols = all_symbols

        return filtered_symbols

    def save_filtered_symbols_to_csv(self, symbols: List[EquitySymbol], filename: str = "filtered_equity_symbols.csv") -> bool:
        """
        Save filtered equity symbols to CSV file to avoid rate limiting issues.

        Args:
            symbols: List of EquitySymbol objects to save
            filename: Name of the CSV file to save

        Returns:
            True if successful, False otherwise
        """
        try:
            import csv
            import os

            # Create reports directory if it doesn't exist
            reports_dir = "reports"
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            filepath = os.path.join(reports_dir, filename)

            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['symbol', 'underlying', 'full_symbol']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for symbol in symbols:
                    writer.writerow({
                        'symbol': symbol.symbol,
                        'underlying': symbol.underlying,
                        'full_symbol': symbol.full_symbol
                    })

            logger.info(f"Saved {len(symbols)} filtered equity symbols to {filepath}")
            return True

        except Exception as e:
            logger.error(f"Error saving filtered equity symbols to CSV: {e}")
            return False